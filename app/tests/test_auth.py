import os
import pytest
from unittest.mock import patch, MagicMock
from uuid import uuid4

from app.services.auth import AuthService

@pytest.fixture
def mock_settings(monkeypatch):
    class SignalRSettings:
        connection_string = (
            "Endpoint=https://qualtestsignalr.service.signalr.net;"
            "AccessKey=AMnCNa0WUM7nloEtx6vyKn0BEeaGZa1Y9C9jlNGc8O79GysnO4MjJQQJ99BEACPV0roXJ3w3AAAAASRSnfTg;Version=1.0;"
        )
        token_lifespan = 10
        hub_name = "testhub"

    class AuthSettings:
        signal_r = SignalRSettings()

    class Settings:
        auth = AuthSettings()

    monkeypatch.setattr("app.services.auth.settings", Settings())

def test_connection_string_parsing(mock_settings):
    service = AuthService()
    assert service.signal_r_access_key == "AMnCNa0WUM7nloEtx6vyKn0BEeaGZa1Y9C9jlNGc8O79GysnO4MjJQQJ99BEACPV0roXJ3w3AAAAASRSnfTg"
    assert service.signal_r_endpoint == "https://qualtestsignalr.service.signalr.net"

@patch("app.services.auth.jwt.encode")
def test_generate_signal_r_jwt(mock_jwt_encode, mock_settings):
    mock_jwt_encode.return_value = "mocked.jwt.token"
    service = AuthService()
    conversation_id = uuid4()
    result = service.generate_signal_r_jwt(conversation_id)
    assert result == {"token": "mocked.jwt.token"}
    assert mock_jwt_encode.called
    # Check claims structure
    claims = mock_jwt_encode.call_args[0][0]
    assert "exp" in claims
    assert "aud" in claims
    assert "sub" in claims
    assert claims["sub"] == str(conversation_id)


def test_real_connection_string_parsing():
    service = AuthService()
    # These asserts will pass if your real settings are configured as expected
    assert hasattr(service, "signal_r_access_key")
    assert hasattr(service, "signal_r_endpoint")
    assert service.signal_r_access_key
    assert service.signal_r_endpoint.startswith("https://")


def test_real_generate_signal_r_jwt():
    service = AuthService()
    conversation_id = uuid4()
    result = service.generate_signal_r_jwt(conversation_id)
    assert "token" in result
    assert isinstance(result["token"], str)
    assert len(result["token"].split(".")) == 3  # JWT format
